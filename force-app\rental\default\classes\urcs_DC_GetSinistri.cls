/**
 * @File Name         : urcs_DC_GetSinistri.cls
 * @Description       :
 * <AUTHOR> VE
 * @Group             :
 * @Last Modified On  : 11-07-2025
 * @Last Modified By  : VE
 * @cicd_tests urcs_DC_GetSinistri
**/
global with sharing class urcs_DC_GetSinistri implements System.Callable {

    public Object call(String action, Map<String,Object> args){
        Map<String,Object> input = (Map<String,Object>) args.get('input');
        Map<String,Object> output = (Map<String,Object>) args.get('output');
        Map<String,Object> options = (Map<String,Object>) args.get('options');
        invokeMethod(input, output, options);
        return null;
    }
        
    @AuraEnabled
    global static Map<String,Object> invokeMethod(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        
        system.debug('input '+ JSON.serialize(input));
        system.debug('output '+ JSON.serialize(output));
        system.debug('options '+ JSON.serialize(options));
        Map<String,Object> setValueMap = (Map<String,Object>) input.get('Set Values');
        String method = (String) setValueMap.get('methodExecute');
        try{
            if(method.equalsIgnoreCase('init')){
                output = getSinistri(input,output,options);
            }
        }catch(Exception e){
            output.put('error',e.getMessage() + ' ' + e.getStackTraceString());
        }
        return output;
    }
    
    public static Map<String,Object> getSinistri(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        try{
            String idVeicolo =  null != (String)input.get('recordId') ? (String)input.get('recordId') : null;
            List<Asset> asset = [SELECT id,ExternalId__c FROM Asset WHERE id=: idVeicolo];
			String externalId = asset.get(0).ExternalId__c.replace('UR_V_','');
            Integer idAuto = Integer.valueOf(externalId);
            system.debug('idVeicolo '+ idVeicolo);
            String queryString = 'SELECT ID_SINISTRO__c,'+
                                    'ID_SINISTRO_WB__c, '+
                                    'DS_STATO_SINISTRO__c,' +
                                    'ID_CONTRATTO__c,'+
                                    'ID_AUTO__c, '+
                                    'DS_DRIVER_DENOMINAZIONECOMPUTED__c, '+
                                    'INTERVENTO_DATA_INVIO__c, '+
                                    'DS_ULTIMA_SORGENTE__c, '+
                                    'TS_INVIO_WB__c, '+
                                    'TS_DENUNCIA__c, '+
                                    'TS_CREAZIONE__c, '+
                                    'TS_CHIUSURA__c, '+
                                    'DS_CANALE_APERTURA__c ' +
                                    'FROM UR_SINISTRI__dlm ' + 
                                    'WHERE ID_AUTO__c =: idAuto ';
            List<sObject> listSinistri = Database.query(queryString);

            output.put('data',listSinistri);
            
        } catch (Exception e){

            output.put('data',null);
            System.debug('error in urcs_DC_GetSinistri method invokeMethod: '+ json.serializePretty(e));
        }

        return output;
    }

}