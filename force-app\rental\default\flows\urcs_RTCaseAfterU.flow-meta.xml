<?xml version="1.0" encoding="UTF-8"?>
<Flow xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionCalls>
        <name>Send_Case_Email_Synch</name>
        <label>Send Case Email Synch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>emailSimple</actionName>
        <actionType>emailSimple</actionType>
        <flowTransactionModel>CurrentTransaction</flowTransactionModel>
        <inputParameters>
            <name>senderType</name>
            <value>
                <stringValue>OrgWideEmailAddress</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>senderAddress</name>
            <value>
                <elementReference>Get_OWA_No_Reply_Synch.Address</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>useEmailTemplate</name>
            <value>
                <stringValue>True</stringValue>
            </value>
        </inputParameters>
        <inputParameters>
            <name>emailTemplateId</name>
            <value>
                <elementReference>Get_Template_Chiusura_Synch.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>recipientId</name>
            <value>
                <elementReference>$Record.ContactId</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>relatedRecordId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>logEmailOnSend</name>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </inputParameters>
        <nameSegment>emailSimple</nameSegment>
        <offset>0</offset>
        <versionString>1.0.1</versionString>
    </actionCalls>
    <actionCalls>
        <name>SendEmailChiusuraToTargetAddressInvocable</name>
        <label>SendEmailChiusuraToTargetAddressInvocable</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <actionName>urcs_SendEmail</actionName>
        <actionType>apex</actionType>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Erore_Generico_Asynch</targetReference>
        </faultConnector>
        <flowTransactionModel>Automatic</flowTransactionModel>
        <inputParameters>
            <name>targetAddress</name>
            <value>
                <elementReference>$Record.EmailChiamante__c</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>templateName</name>
            <value>
                <elementReference>Get_Template_Chiusura_Asynch.Name</elementReference>
            </value>
        </inputParameters>
        <inputParameters>
            <name>whatId</name>
            <value>
                <elementReference>$Record.Id</elementReference>
            </value>
        </inputParameters>
        <nameSegment>urcs_SendEmail</nameSegment>
        <offset>0</offset>
    </actionCalls>
    <apiVersion>64.0</apiVersion>
    <areMetricsLoggedToDataCloud>false</areMetricsLoggedToDataCloud>
    <customErrors>
        <name>Erore_Generico_Asynch</name>
        <label>Erore Generico Asynch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <customErrorMessages>
            <errorMessage>Si è verificato un errore: impossibile completare l&apos;operazione.
 
{!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <customErrors>
        <name>Erore_Generico_Synch</name>
        <label>Erore Generico Synch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <customErrorMessages>
            <errorMessage>Si è verificato un errore: impossibile completare l&apos;operazione.
 
{!$Flow.FaultMessage}</errorMessage>
            <isFieldError>false</isFieldError>
        </customErrorMessages>
    </customErrors>
    <decisions>
        <name>Check_destinatario_mail_Chiusura_Asynch</name>
        <label>Check destinatario mail Chiusura Asynch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnector>
            <targetReference>SendEmailChiusuraToTargetAddressInvocable</targetReference>
        </defaultConnector>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Email_Chiamante_Contact_Email2</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.EmailChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>$Record.Contact.Email</elementReference>
                </rightValue>
            </conditions>
            <label>Email Chiamante == Contact.Email</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_destinatario_mail_Chiusura_Synch</name>
        <label>Check destinatario mail Chiusura Synch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Email_Chiamante_Contact_Email_Synch</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.EmailChiamante__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <elementReference>$Record.Contact.Email</elementReference>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Send_Case_Email_Synch</targetReference>
            </connector>
            <label>Email Chiamante == Contact.Email Synch</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_Email_Template_Chiusura_Asynch</name>
        <label>Check Email Template Chiusura Asynch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>ET_Chiusura_not_null</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>Get_Template_Chiusura_Asynch</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Check_destinatario_mail_Chiusura_Asynch</targetReference>
            </connector>
            <label>ET Chiusura not null</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_OWA_Asynch</name>
        <label>Check OWA Asynch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Copy_1_of_Send_Email</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.InvioEmailAutomatico__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_OWA_No_Reply_Asynch</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Template_Chiusura_Asynch</targetReference>
            </connector>
            <label>Send Email</label>
        </rules>
    </decisions>
    <decisions>
        <name>Check_OWA_Synch</name>
        <label>Check OWA Synch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Send_Email_Synch</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.InvioEmailAutomatico__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>Get_OWA_No_Reply_Synch</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_Template_Chiusura_Synch</targetReference>
            </connector>
            <label>Send Email Synch</label>
        </rules>
    </decisions>
    <decisions>
        <name>Chek_conditions_for_email_chiusura_Asynch</name>
        <label>Chek conditions for email chiusura Asynch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Verified</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.InvioEmailAutomatico__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Chiuso - Risolto</stringValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_OWA_No_Reply_Asynch</targetReference>
            </connector>
            <label>Verified</label>
        </rules>
    </decisions>
    <decisions>
        <name>Chek_conditions_for_email_chiusura_Synch</name>
        <label>Chek conditions for email chiusura Synch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <defaultConnectorLabel>Default Outcome</defaultConnectorLabel>
        <rules>
            <name>Verified_Synch</name>
            <conditionLogic>and</conditionLogic>
            <conditions>
                <leftValueReference>$Record.InvioEmailAutomatico__c</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <booleanValue>true</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Status</leftValueReference>
                <operator>EqualTo</operator>
                <rightValue>
                    <stringValue>Chiuso - Risolto</stringValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.ContactId</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <conditions>
                <leftValueReference>$Record.Contact.Email</leftValueReference>
                <operator>IsNull</operator>
                <rightValue>
                    <booleanValue>false</booleanValue>
                </rightValue>
            </conditions>
            <connector>
                <targetReference>Get_OWA_No_Reply_Synch</targetReference>
            </connector>
            <label>Verified Synch</label>
        </rules>
    </decisions>
    <environments>Default</environments>
    <formulas>
        <name>is_new</name>
        <dataType>Boolean</dataType>
        <expression>ISNEW()</expression>
    </formulas>
    <interviewLabel>urcs_RTCaseAfterU {!$Flow.CurrentDateTime}</interviewLabel>
    <label>urcs_RTCaseAfterU</label>
    <processMetadataValues>
        <name>BuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>CanvasMode</name>
        <value>
            <stringValue>AUTO_LAYOUT_CANVAS</stringValue>
        </value>
    </processMetadataValues>
    <processMetadataValues>
        <name>OriginBuilderType</name>
        <value>
            <stringValue>LightningFlowBuilder</stringValue>
        </value>
    </processMetadataValues>
    <processType>AutoLaunchedFlow</processType>
    <recordLookups>
        <name>Get_OWA_No_Reply_Asynch</name>
        <label>Get OWA No Reply Asynch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_OWA_Asynch</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Erore_Generico_Asynch</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DisplayName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Unipol Rental CS No Reply</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsVerified</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>OrgWideEmailAddress</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_OWA_No_Reply_Synch</name>
        <label>Get OWA No Reply Synch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_OWA_Synch</targetReference>
        </connector>
        <faultConnector>
            <targetReference>Erore_Generico_Synch</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>DisplayName</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>Unipol Rental CS No Reply</stringValue>
            </value>
        </filters>
        <filters>
            <field>IsVerified</field>
            <operator>EqualTo</operator>
            <value>
                <booleanValue>true</booleanValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>OrgWideEmailAddress</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Template_Chiusura_Asynch</name>
        <label>Get Template Chiusura Asynch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_Email_Template_Chiusura_Asynch</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Erore_Generico_Asynch</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>urcs_CaseNotificaChiusura</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>EmailTemplate</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <recordLookups>
        <name>Get_Template_Chiusura_Synch</name>
        <label>Get Template Chiusura Synch</label>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <assignNullValuesIfNoRecordsFound>false</assignNullValuesIfNoRecordsFound>
        <connector>
            <targetReference>Check_destinatario_mail_Chiusura_Synch</targetReference>
        </connector>
        <faultConnector>
            <isGoTo>true</isGoTo>
            <targetReference>Erore_Generico_Synch</targetReference>
        </faultConnector>
        <filterLogic>and</filterLogic>
        <filters>
            <field>Name</field>
            <operator>EqualTo</operator>
            <value>
                <stringValue>urcs_CaseNotificaChiusura</stringValue>
            </value>
        </filters>
        <getFirstRecordOnly>true</getFirstRecordOnly>
        <object>EmailTemplate</object>
        <storeOutputAutomatically>true</storeOutputAutomatically>
    </recordLookups>
    <start>
        <locationX>0</locationX>
        <locationY>0</locationY>
        <connector>
            <targetReference>Chek_conditions_for_email_chiusura_Synch</targetReference>
        </connector>
        <doesRequireRecordChangedToMeetCriteria>true</doesRequireRecordChangedToMeetCriteria>
        <filterFormula>AND(
       {!$Setup.urcs_GeneralSettings__c.SkipTrigger__c} == false,
        OR(
              {!$Record.RecordType.DeveloperName} == &apos;ur_CaseCRM&apos;,
             {!$Record.RecordType.DeveloperName} ==  &apos;ur_CaseAR&apos;,
             {!$Record.RecordType.DeveloperName} == &apos;ur_CasePQ&apos;,
             {!$Record.RecordType.DeveloperName} == &apos;ur_CaseSitoWeb&apos;,
             {!$Record.RecordType.DeveloperName}  == &apos;ur_CaseES&apos;
        ),
        TEXT({!$Record.Status}) == &apos;Chiuso - Risolto&apos;
)</filterFormula>
        <object>Case</object>
        <recordTriggerType>Update</recordTriggerType>
        <scheduledPaths>
            <connector>
                <targetReference>Chek_conditions_for_email_chiusura_Asynch</targetReference>
            </connector>
            <pathType>AsyncAfterCommit</pathType>
        </scheduledPaths>
        <triggerType>RecordAfterSave</triggerType>
    </start>
    <status>Active</status>
</Flow>
