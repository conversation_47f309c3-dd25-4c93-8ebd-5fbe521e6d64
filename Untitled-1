	"Field  ApiName"	"Field  Label"	"Field Label Translation IT"	"is Standard"	"is Required"	"Is  ExternalId"	"Is Unique"	"Is Case Sensitive"	Data Type	"Field  Legnth"	"Default  Value"	"Picklist  Values  Apiname"	"Picklist  Values   Label"	"Picklist  Values Translation IT"	"RT: ur_CaseCRM"	"RT: ur_CaseAR"	"RT: ur_CaseSitoWeb"	"RT: ur_CasePQ"	"RT: ur_CaseES"	Technical Notes	"Functional  Description"	"Profile:  System Administrator"	"Profile:  Unipol Rental CS Standard User"	"Permission Set:  Unipol Rental CS Operatore urcs_Operatore"	"Permission Set:  Unipol Rental CS Admin urcs_Admin"	"Permission Set:  Unipol Rental CS Mulesoft Integration urcs_MulesoftIntegration"					

CRM-59	IdClienteLeo__c	Id cliente Leo		N	N				Formula(Text)	255						Y		Y		Campo tecnico per riportare la info da account correlato	Campo tecnico non visibile.	R	/	R	R	R					