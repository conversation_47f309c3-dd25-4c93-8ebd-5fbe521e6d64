/**
 * @File Name         : urcs_DC_GetOptional.cls
 * @Description       : 
 * <AUTHOR> VE
 * @Group             : 
 * @Last Modified On  : 11-07-2025
 * @Last Modified By  : VE
**/
global with sharing class urcs_DC_GetOptional implements System.Callable {

    public Object call(String action, Map<String,Object> args){
        Map<String,Object> input = (Map<String,Object>) args.get('input');
        Map<String,Object> output = (Map<String,Object>) args.get('output');
        Map<String,Object> options = (Map<String,Object>) args.get('options');
        invokeMethod(input, output, options);
        return null;
    }
        
    @AuraEnabled
    global static Map<String,Object> invokeMethod(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        
        system.debug('input '+ JSON.serialize(input));
        system.debug('output '+ JSON.serialize(output));
        system.debug('options '+ JSON.serialize(options));
        Map<String,Object> setValueMap = (Map<String,Object>) input.get('Set Values');
        String method = (String) setValueMap.get('methodExecute');
        try{
            if(method.equalsIgnoreCase('init')){
                output = getOptional(input,output,options);
            }
        }catch(Exception e){
            output.put('error',e.getMessage() + ' ' + e.getStackTraceString());
        }
        return output;
    }
    
    public static Map<String,Object> getOptional(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        try{
            String idVeicolo =  null != (String)input.get('recordId') ? (String)input.get('recordId') : null;
            List<Asset> asset = [SELECT id,ExternalId__c FROM Asset WHERE id=: idVeicolo];
            String externalId = asset.get(0).ExternalId__c.replace('UR_V_','');
            Integer idAuto = Integer.valueOf(externalId);
            system.debug('idVeicolo '+ idVeicolo);
            String queryString = 'SELECT DS_TIPO_OPTIONAL__c, '+
                                    'DS_OPTIONAL__c ' +
                                    'FROM UR_OPTIONAL__dlm ' +
                                    'WHERE ID_AUTO__c =: idAuto ';
            List<sObject> listOptional = Database.query(queryString);

            output.put('data',listOptional);

        } catch (Exception e){

            output.put('data',null);
            System.debug('error in urcs_DC_GetOptional method invokeMethod: '+ json.serializePretty(e));
        }

        return output;
    }
}