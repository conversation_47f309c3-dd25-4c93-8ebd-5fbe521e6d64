------------------------------------------------------------------------------
--------------------------------- DESCRIPTION ---------------------------------
-------------------------------------------------------------------------------
 
This manual procedure aims to change email templates
 
-------------------------------------------------------------------------------
--------------------------- MANUAL PROCEDURE STEPS ----------------------------
-------------------------------------------------------------------------------
 
1. Login to Salesforce

2. Modify Case Creation Notification Email Template
   - Search for "Email Templates" in App Launcher
   - Locate and open: urcs_CaseNotificaCreazione
   - In HTML Value section, click Source
   - Replace the existing HTML code with the following corrected version:

<html style="overflow-y: hidden;">
<head>
	<title></title>
</head>
<body style="height: auto; min-height: auto;">
<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Gentile {{{Case.NomeChiamante__c}}},</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">le confermiamo di aver ricevuto la richiesta <strong>{{{Case.CaseNumber}}}</strong>, aperta in data <strong>{{{Case.CreatedDate}}}</strong>.</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Di seguito il riepilogo:<br />
<strong>Categoria</strong>: {{{Case.Categoria__c}}}<br />
<strong>Sotto-categoria</strong>: {{{Case.SottoCategoria__c}}}<br />
<strong>Richiesta</strong>: {{{Case.Subject}}}<br />
<strong>Targa</strong>: {{{Case.TargaVeicoloFormula__c}}}<br />
<strong>Contratto</strong>: {{{Case.ContractNameFormula__c}}}</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Il nostro team sta analizzando la sua richiesta e la contatter&agrave; al pi&ugrave; presto per fornirle maggiori dettagli o aggiornamenti.</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">La ringraziamo per aver scelto UnipolRental.<br />
Buona giornata,<br />
Servizio clienti UnipolRental</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;"><em>Questa &egrave; una mail automatica inviata da un indirizzo no-reply. La invitiamo a non rispondere.</em></span></p>
</body>
</html>

3.Save


4. Modify Case Closure Notification Email Template
   - Locate and open: urcs_CaseNotificaChiusura
   - In HTML Value section, click Source
   - Replace the existing HTML code with the following corrected version:

<html style="overflow-y: hidden;">
<head>
	<title></title>
</head>
<body style="height: auto; min-height: auto;">
<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Gentile {{{Case.NomeChiamante__c}}},</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Le confermiamo che la sua richiesta <strong>{{{Case.CaseNumber}}}</strong>, aperta in data <strong>{{{Case.CreatedDate}}}</strong>, &egrave; stata gestita e risolta con successo.</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;">Di seguito il riepilogo:<br />
<strong>Categoria</strong>: {{{Case.Categoria__c}}}<br />
<strong>Sotto-categoria</strong>: {{{Case.SottoCategoria__c}}}<br />
<strong>Richiesta</strong>: {{{Case.Subject}}}<br />
<strong>Targa</strong>: {{{Case.TargaVeicoloFormula__c}}}<br />
<strong>Contratto</strong>: {{{Case.ContractNameFormula__c}}}<br />
<br />
La ringraziamo per aver scelto UnipolRental.<br />
Buona giornata,<br />
Servizio clienti UnipolRental</span></p>

<p><span style="font-family:Gill Sans,Arial,Helvetica,sans-serif;"><em>Questa &egrave; una mail automatica inviata da un indirizzo no-reply. La invitiamo a non rispondere.</em></span></p>
</body>
</html>

5.Save

6. Open Setup
7. search for flows
8. Delete following flow: urcs_RTCaseAfterIU 